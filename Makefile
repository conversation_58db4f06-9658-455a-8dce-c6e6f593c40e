.PHONY: help build test clean example docs deps check

# Default target
help:
	@echo "WhatsApp FFI Rust Workspace"
	@echo "=========================="
	@echo ""
	@echo "Available targets:"
	@echo "  build    - Build the entire workspace"
	@echo "  test     - Run all tests"
	@echo "  example  - Run the example usage"
	@echo "  docs     - Generate and open documentation"
	@echo "  deps     - Download and update dependencies"
	@echo "  check    - Check code without building"
	@echo "  clean    - Clean build artifacts"
	@echo "  help     - Show this help message"

# Build the entire workspace
build:
	@echo "🔨 Building WhatsApp FFI workspace..."
	@cd whatsapp-ffi/go && go mod tidy
	@cargo build --release
	@echo "✅ Build completed successfully!"

# Run tests
test:
	@echo "🧪 Running tests..."
	@cargo test
	@echo "✅ Tests completed!"

# Run the example
example:
	@echo "🚀 Running example usage..."
	@cargo run --bin example-usage

# Generate documentation
docs:
	@echo "📚 Generating documentation..."
	@cargo doc --open --no-deps

# Download and update dependencies
deps:
	@echo "📦 Updating dependencies..."
	@cd whatsapp-ffi/go && go mod tidy && go mod download
	@cargo update
	@echo "✅ Dependencies updated!"

# Check code without building
check:
	@echo "🔍 Checking code..."
	@cargo check
	@echo "✅ Code check completed!"

# Clean build artifacts
clean:
	@echo "🧹 Cleaning build artifacts..."
	@cargo clean
	@rm -rf whatsapp-ffi/go/go.sum
	@echo "✅ Clean completed!"

# Quick development cycle
dev: check test
	@echo "✅ Development cycle completed!"

# Full build and test
all: deps build test
	@echo "✅ Full build and test completed!"
