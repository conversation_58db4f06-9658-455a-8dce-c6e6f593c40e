use whatsapp_ffi::*;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("WhatsApp FFI Example Usage");
    println!("==========================");

    // Initialize the WhatsApp client
    println!("Initializing WhatsApp client...");
    match init() {
        Ok(_) => println!("✅ WhatsApp client initialized successfully"),
        Err(e) => {
            println!("❌ Failed to initialize WhatsApp client: {}", e);
            return Ok(());
        }
    }

    // Check connection status
    println!("\nChecking connection status...");
    match app::App::status() {
        Ok(status) => {
            println!("📊 Connection Status:");
            println!("  - Connected: {}", status.is_connected);
            println!("  - Logged in: {}", status.is_logged_in);
            println!("  - Device ID: {}", status.device_id);
        }
        Err(e) => println!("❌ Failed to get status: {}", e),
    }

    // If not logged in, show login options
    if let Ok(status) = app::App::status() {
        if !status.is_logged_in {
            println!("\n🔐 Not logged in. Login options:");
            
            // Option 1: QR Code login
            println!("\n1. QR Code Login:");
            match app::App::login() {
                Ok(login_response) => {
                    println!("  📱 QR Code available at: {}", login_response.qr_link);
                    println!("  ⏰ QR Code expires in: {} seconds", login_response.qr_duration);
                    println!("  📲 Scan this QR code with your WhatsApp mobile app");
                }
                Err(e) => println!("  ❌ Failed to get QR code: {}", e),
            }

            // Option 2: Phone number login (example)
            println!("\n2. Phone Number Login (example):");
            match app::App::login_with_code("1234567890") {
                Ok(code_response) => {
                    println!("  🔢 Pairing code: {}", code_response.pair_code);
                    println!("  📱 Enter this code in your WhatsApp mobile app");
                }
                Err(e) => println!("  ❌ Failed to get pairing code: {}", e),
            }
        }
    }

    // Example API calls (these will work only if logged in)
    println!("\n📋 Example API Calls:");
    
    // Send a text message (example)
    println!("\n📤 Sending text message example:");
    let message_request = MessageRequest {
        phone: "1234567890".to_string(),
        message: "Hello from Rust WhatsApp FFI! 🦀".to_string(),
        reply_message_id: None,
        duration: None,
        is_forwarded: None,
    };
    
    match send::Send::message(&message_request) {
        Ok(response) => {
            println!("  ✅ Message sent successfully!");
            println!("  📨 Status: {}", response.status);
            if let Some(msg_id) = response.message_id {
                println!("  🆔 Message ID: {}", msg_id);
            }
        }
        Err(e) => println!("  ❌ Failed to send message: {}", e),
    }

    // Get user info (example)
    println!("\n👤 Getting user info example:");
    let user_info_request = UserInfoRequest {
        phone: "1234567890".to_string(),
    };
    
    match user::User::info(&user_info_request) {
        Ok(info) => {
            println!("  ✅ User info retrieved:");
            println!("  📱 Phone: {}", info.phone);
            println!("  👤 Name: {}", info.name);
            println!("  📝 Status: {}", info.status);
        }
        Err(e) => println!("  ❌ Failed to get user info: {}", e),
    }

    // List chats (example)
    println!("\n💬 Listing chats example:");
    let chat_list_request = ListChatsRequest {
        limit: Some(5),
        offset: Some(0),
        search: None,
        has_media: None,
    };
    
    match chat::Chat::list(&chat_list_request) {
        Ok(chats) => {
            println!("  ✅ Found {} chats:", chats.len());
            for (i, chat) in chats.iter().enumerate() {
                println!("  {}. {} ({})", i + 1, chat.name, chat.jid);
                if chat.is_group {
                    println!("     👥 Group chat");
                }
                if chat.is_pinned {
                    println!("     📌 Pinned");
                }
                println!("     💬 Unread: {}", chat.unread_count);
            }
        }
        Err(e) => println!("  ❌ Failed to list chats: {}", e),
    }

    // Create a group (example)
    println!("\n👥 Creating group example:");
    let create_group_request = CreateGroupRequest {
        name: "Rust WhatsApp FFI Test Group".to_string(),
        participants: vec!["1234567890".to_string(), "0987654321".to_string()],
    };
    
    match group::Group::create(&create_group_request) {
        Ok(response) => {
            println!("  ✅ Group created successfully!");
            println!("  🆔 Group ID: {}", response.group_id);
        }
        Err(e) => println!("  ❌ Failed to create group: {}", e),
    }

    // Send presence (example)
    println!("\n👋 Setting presence example:");
    let presence_request = PresenceRequest {
        state: "available".to_string(),
    };
    
    match send::Send::presence(&presence_request) {
        Ok(response) => {
            println!("  ✅ Presence set successfully!");
            println!("  📊 Status: {}", response.status);
        }
        Err(e) => println!("  ❌ Failed to set presence: {}", e),
    }

    // Check users on WhatsApp (example)
    println!("\n🔍 Checking users on WhatsApp example:");
    let check_request = CheckUserRequest {
        phones: vec!["1234567890".to_string(), "0987654321".to_string()],
    };
    
    match user::User::check(&check_request) {
        Ok(results) => {
            println!("  ✅ Check results:");
            for result in results {
                println!("  📱 {}: {}", result.phone, if result.exists { "✅ On WhatsApp" } else { "❌ Not on WhatsApp" });
            }
        }
        Err(e) => println!("  ❌ Failed to check users: {}", e),
    }

    println!("\n🎉 Example completed!");
    println!("\n📚 Available APIs:");
    println!("  🔐 App: login, logout, reconnect, devices, status");
    println!("  📤 Send: message, image, file, video, contact, link, location, audio, poll, presence");
    println!("  👤 User: info, avatar, change_avatar, change_pushname, privacy, groups, contacts, check, business_profile");
    println!("  💬 Chat: list, messages, pin");
    println!("  👥 Group: create, join, info, leave, manage_participants, set_photo, set_name, etc.");
    println!("  📨 Message: react, revoke, delete, update, mark_as_read, star, unstar");
    println!("  📰 Newsletter: unfollow");

    Ok(())
}
