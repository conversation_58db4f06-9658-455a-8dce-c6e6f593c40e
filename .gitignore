# Rust
/target/
**/*.rs.bk
*.pdb
Cargo.lock

reference_code

# Go
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work
go.work.sum

# Go build artifacts in FFI directory
whatsapp-ffi/go/*.a
whatsapp-ffi/go/*.h
whatsapp-ffi/go/*.so
whatsapp-ffi/go/*.dylib
whatsapp-ffi/go/*.dll

# Build outputs
/bin/
/pkg/
*.a
*.lib

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Environment files
.env
.env.local
.env.*.local

# Temporary files
*.tmp
*.temp
/tmp/

# Documentation build
/book/
/docs/_build/

# Coverage reports
*.profraw
*.gcov
lcov.info
coverage/

# Backup files
*.bak
*.backup

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# WhatsApp specific
# Session files (if any)
session/
*.session
qrcode/
*.qr

# Media files that might be generated during testing
media/
uploads/
downloads/
statics/media/*
statics/qrcode/*
statics/senditems/*

# Database files
*.db
*.sqlite
*.sqlite3

# Configuration files with sensitive data
config.json
settings.json
.config

# FFI build artifacts
*.h
libwhatsapp.*

# Rust documentation
target/doc/

# Benchmark results
criterion/

# Flamegraph
flamegraph.svg
perf.data*
