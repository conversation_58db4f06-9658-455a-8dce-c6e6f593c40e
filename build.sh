#!/bin/bash

echo "🚀 Building WhatsApp FFI Rust Workspace"
echo "======================================="

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed. Please install Go 1.21+ first."
    exit 1
fi

# Check if Rust is installed
if ! command -v cargo &> /dev/null; then
    echo "❌ Rust is not installed. Please install Rust first."
    exit 1
fi

echo "✅ Go version: $(go version)"
echo "✅ Rust version: $(rustc --version)"

# Navigate to Go directory and download dependencies
echo ""
echo "📦 Downloading Go dependencies..."
cd whatsapp-ffi/go
go mod tidy
if [ $? -ne 0 ]; then
    echo "❌ Failed to download Go dependencies"
    exit 1
fi
cd ../..

# Build the Rust workspace
echo ""
echo "🔨 Building Rust workspace..."
cargo build --release
if [ $? -ne 0 ]; then
    echo "❌ Failed to build Rust workspace"
    exit 1
fi

echo ""
echo "✅ Build completed successfully!"
echo ""
echo "📚 Available commands:"
echo "  cargo run --bin example-usage  # Run the example"
echo "  cargo test                     # Run tests"
echo "  cargo doc --open              # Generate and open documentation"
echo ""
echo "🎉 WhatsApp FFI library is ready to use!"
