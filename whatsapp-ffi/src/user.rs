use crate::types::*;
use crate::{call_go_function, call_go_function_with_request, parse_response, parse_void_response};
use std::os::raw::c_char;

// External C functions from Go
extern "C" {
    fn user_info(request: *mut c_char) -> *mut c_char;
    fn user_avatar(request: *mut c_char) -> *mut c_char;
    fn user_change_avatar(request: *mut c_char) -> *mut c_char;
    fn user_change_pushname(request: *mut c_char) -> *mut c_char;
    fn user_my_privacy() -> *mut c_char;
    fn user_my_groups() -> *mut c_char;
    fn user_my_newsletters() -> *mut c_char;
    fn user_my_contacts() -> *mut c_char;
    fn user_check(request: *mut c_char) -> *mut c_char;
    fn user_business_profile(request: *mut c_char) -> *mut c_char;
}

/// User API functions
pub struct User;

impl User {
    /// Get user information
    pub fn info(request: &UserInfoRequest) -> Result<UserInfo, WhatsAppError> {
        let response_str = call_go_function_with_request(user_info, request)?;
        parse_response(&response_str)
    }

    /// Get user avatar
    pub fn avatar(request: &AvatarRequest) -> Result<AvatarResponse, WhatsAppError> {
        let response_str = call_go_function_with_request(user_avatar, request)?;
        parse_response(&response_str)
    }

    /// Change user avatar
    pub fn change_avatar(request: &ChangeAvatarRequest) -> Result<(), WhatsAppError> {
        let response_str = call_go_function_with_request(user_change_avatar, request)?;
        parse_void_response(&response_str)
    }

    /// Change user push name
    pub fn change_pushname(request: &ChangePushNameRequest) -> Result<(), WhatsAppError> {
        let response_str = call_go_function_with_request(user_change_pushname, request)?;
        parse_void_response(&response_str)
    }

    /// Get my privacy settings
    pub fn my_privacy() -> Result<PrivacySetting, WhatsAppError> {
        let response_str = call_go_function(user_my_privacy)?;
        parse_response(&response_str)
    }

    /// Get my groups
    pub fn my_groups() -> Result<Vec<GroupInfo>, WhatsAppError> {
        let response_str = call_go_function(user_my_groups)?;
        parse_response(&response_str)
    }

    /// Get my newsletters
    pub fn my_newsletters() -> Result<Vec<serde_json::Value>, WhatsAppError> {
        let response_str = call_go_function(user_my_newsletters)?;
        parse_response(&response_str)
    }

    /// Get my contacts
    pub fn my_contacts() -> Result<Vec<ContactInfo>, WhatsAppError> {
        let response_str = call_go_function(user_my_contacts)?;
        parse_response(&response_str)
    }

    /// Check if users are on WhatsApp
    pub fn check(request: &CheckUserRequest) -> Result<Vec<CheckUserResponse>, WhatsAppError> {
        let response_str = call_go_function_with_request(user_check, request)?;
        parse_response(&response_str)
    }

    /// Get business profile
    pub fn business_profile(request: &BusinessProfileRequest) -> Result<BusinessProfile, WhatsAppError> {
        let response_str = call_go_function_with_request(user_business_profile, request)?;
        parse_response(&response_str)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_user_info() {
        let request = UserInfoRequest {
            phone: "1234567890".to_string(),
        };

        match User::info(&request) {
            Ok(info) => {
                println!("User info: {:?}", info);
            }
            Err(e) => {
                println!("User info failed (expected if not initialized): {}", e);
            }
        }
    }

    #[test]
    fn test_check_user() {
        let request = CheckUserRequest {
            phones: vec!["1234567890".to_string(), "0987654321".to_string()],
        };

        match User::check(&request) {
            Ok(results) => {
                println!("Check results: {:?}", results);
            }
            Err(e) => {
                println!("Check failed (expected if not initialized): {}", e);
            }
        }
    }
}
