use crate::types::*;
use crate::{call_go_function_with_request, parse_response, parse_void_response};
use std::os::raw::c_char;

// External C functions from Go
extern "C" {
    fn message_react(request: *mut c_char) -> *mut c_char;
    fn message_revoke(request: *mut c_char) -> *mut c_char;
    fn message_delete(request: *mut c_char) -> *mut c_char;
    fn message_update(request: *mut c_char) -> *mut c_char;
    fn message_mark_as_read(request: *mut c_char) -> *mut c_char;
    fn message_star(request: *mut c_char) -> *mut c_char;
    fn message_unstar(request: *mut c_char) -> *mut c_char;
}

/// Message API functions
pub struct Message;

impl Message {
    /// React to a message
    pub fn react(request: &ReactionRequest) -> Result<MessageActionResponse, WhatsAppError> {
        let response_str = call_go_function_with_request(message_react, request)?;
        parse_response(&response_str)
    }

    /// Revoke a message
    pub fn revoke(request: &RevokeRequest) -> Result<MessageActionResponse, WhatsAppError> {
        let response_str = call_go_function_with_request(message_revoke, request)?;
        parse_response(&response_str)
    }

    /// Delete a message
    pub fn delete(request: &DeleteRequest) -> Result<(), WhatsAppError> {
        let response_str = call_go_function_with_request(message_delete, request)?;
        parse_void_response(&response_str)
    }

    /// Update a message
    pub fn update(request: &UpdateMessageRequest) -> Result<MessageActionResponse, WhatsAppError> {
        let response_str = call_go_function_with_request(message_update, request)?;
        parse_response(&response_str)
    }

    /// Mark message as read
    pub fn mark_as_read(request: &MarkAsReadRequest) -> Result<MessageActionResponse, WhatsAppError> {
        let response_str = call_go_function_with_request(message_mark_as_read, request)?;
        parse_response(&response_str)
    }

    /// Star a message
    pub fn star(request: &StarRequest) -> Result<(), WhatsAppError> {
        let response_str = call_go_function_with_request(message_star, request)?;
        parse_void_response(&response_str)
    }

    /// Unstar a message
    pub fn unstar(request: &StarRequest) -> Result<(), WhatsAppError> {
        let response_str = call_go_function_with_request(message_unstar, request)?;
        parse_void_response(&response_str)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_message_react() {
        let request = ReactionRequest {
            message_id: "message123".to_string(),
            phone: "1234567890".to_string(),
            emoji: "👍".to_string(),
        };

        match Message::react(&request) {
            Ok(response) => {
                println!("Message reacted: {:?}", response);
            }
            Err(e) => {
                println!("Message react failed (expected if not initialized): {}", e);
            }
        }
    }

    #[test]
    fn test_message_star() {
        let request = StarRequest {
            message_id: "message123".to_string(),
            phone: "1234567890".to_string(),
        };

        match Message::star(&request) {
            Ok(_) => {
                println!("Message starred successfully");
            }
            Err(e) => {
                println!("Message star failed (expected if not initialized): {}", e);
            }
        }
    }
}
