use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct FFIResponse<T> {
    pub status: i32,
    pub code: String,
    pub message: String,
    pub results: Option<T>,
    pub error: Option<String>,
}

// ============ APP TYPES ============

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct LoginResponse {
    pub qr_link: String,
    pub qr_duration: i32,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct LoginWithCodeResponse {
    pub pair_code: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DeviceInfo {
    pub device_id: String,
    pub device_name: String,
    pub device_type: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ConnectionStatus {
    pub is_connected: bool,
    pub is_logged_in: bool,
    pub device_id: String,
}

// ============ SEND TYPES ============

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MessageRequest {
    pub phone: String,
    pub message: String,
    pub reply_message_id: Option<String>,
    pub duration: Option<i32>,
    pub is_forwarded: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImageRequest {
    pub phone: String,
    pub image_url: Option<String>,
    pub caption: Option<String>,
    pub reply_message_id: Option<String>,
    pub compress: Option<bool>,
    pub duration: Option<i32>,
    pub is_forwarded: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileRequest {
    pub phone: String,
    pub file_url: Option<String>,
    pub caption: Option<String>,
    pub reply_message_id: Option<String>,
    pub duration: Option<i32>,
    pub is_forwarded: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VideoRequest {
    pub phone: String,
    pub video_url: Option<String>,
    pub caption: Option<String>,
    pub reply_message_id: Option<String>,
    pub duration: Option<i32>,
    pub is_forwarded: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContactRequest {
    pub phone: String,
    pub contact_name: String,
    pub contact_phone: String,
    pub reply_message_id: Option<String>,
    pub duration: Option<i32>,
    pub is_forwarded: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LinkRequest {
    pub phone: String,
    pub link: String,
    pub caption: Option<String>,
    pub reply_message_id: Option<String>,
    pub duration: Option<i32>,
    pub is_forwarded: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LocationRequest {
    pub phone: String,
    pub latitude: f64,
    pub longitude: f64,
    pub name: Option<String>,
    pub address: Option<String>,
    pub reply_message_id: Option<String>,
    pub duration: Option<i32>,
    pub is_forwarded: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AudioRequest {
    pub phone: String,
    pub audio_url: Option<String>,
    pub reply_message_id: Option<String>,
    pub duration: Option<i32>,
    pub is_forwarded: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PollRequest {
    pub phone: String,
    pub question: String,
    pub options: Vec<String>,
    pub selectable_count: Option<i32>,
    pub reply_message_id: Option<String>,
    pub duration: Option<i32>,
    pub is_forwarded: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PresenceRequest {
    pub state: String, // "available", "unavailable", "composing", "recording", "paused"
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatPresenceRequest {
    pub phone: String,
    pub state: String, // "composing", "paused"
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SendResponse {
    pub status: String,
    pub message_id: Option<String>,
    pub timestamp: Option<i64>,
}

// ============ USER TYPES ============

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserInfoRequest {
    pub phone: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserInfo {
    pub jid: String,
    pub phone: String,
    pub name: String,
    pub status: String,
    pub picture_url: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AvatarRequest {
    pub phone: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AvatarResponse {
    pub url: String,
    pub id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChangeAvatarRequest {
    pub avatar_url: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChangePushNameRequest {
    pub push_name: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PrivacySetting {
    pub read_receipts: String,
    pub profile_photo: String,
    pub status: String,
    pub last_seen: String,
    pub groups: String,
    pub calls: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GroupInfo {
    pub jid: String,
    pub name: String,
    pub topic: String,
    pub participants: Vec<GroupParticipant>,
    pub admins: Vec<String>,
    pub owner: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GroupParticipant {
    pub jid: String,
    pub phone: String,
    pub name: String,
    pub is_admin: bool,
    pub is_super_admin: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContactInfo {
    pub jid: String,
    pub phone: String,
    pub name: String,
    pub notify: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CheckUserRequest {
    pub phones: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CheckUserResponse {
    pub phone: String,
    pub exists: bool,
    pub jid: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BusinessProfileRequest {
    pub phone: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BusinessProfile {
    pub jid: String,
    pub name: String,
    pub category: String,
    pub description: String,
    pub website: String,
    pub email: String,
    pub address: String,
}

// ============ CHAT TYPES ============

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ListChatsRequest {
    pub limit: Option<i32>,
    pub offset: Option<i32>,
    pub search: Option<String>,
    pub has_media: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatInfo {
    pub jid: String,
    pub name: String,
    pub last_message: Option<String>,
    pub last_message_time: Option<i64>,
    pub unread_count: i32,
    pub is_group: bool,
    pub is_pinned: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetChatMessagesRequest {
    pub chat_jid: String,
    pub limit: Option<i32>,
    pub offset: Option<i32>,
    pub media_only: Option<bool>,
    pub search: Option<String>,
    pub start_time: Option<String>,
    pub end_time: Option<String>,
    pub is_from_me: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageInfo {
    pub id: String,
    pub from: String,
    pub to: String,
    pub message: String,
    pub timestamp: i64,
    pub is_from_me: bool,
    pub message_type: String,
    pub media_url: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PinChatRequest {
    pub chat_jid: String,
    pub pin: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PinChatResponse {
    pub message: String,
    pub success: bool,
}

// ============ GROUP TYPES ============

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateGroupRequest {
    pub name: String,
    pub participants: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateGroupResponse {
    pub group_id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JoinGroupWithLinkRequest {
    pub link: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetGroupInfoFromLinkRequest {
    pub link: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GroupInfoRequest {
    pub group_id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LeaveGroupRequest {
    pub group_id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParticipantRequest {
    pub group_id: String,
    pub participants: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetGroupRequestParticipantsRequest {
    pub group_id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GroupRequestParticipantsRequest {
    pub group_id: String,
    pub participants: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SetGroupPhotoRequest {
    pub group_id: String,
    pub photo_url: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SetGroupPhotoResponse {
    pub picture_id: String,
    pub message: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SetGroupNameRequest {
    pub group_id: String,
    pub name: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SetGroupLockedRequest {
    pub group_id: String,
    pub locked: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SetGroupAnnounceRequest {
    pub group_id: String,
    pub announce: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SetGroupTopicRequest {
    pub group_id: String,
    pub topic: String,
}

// ============ MESSAGE TYPES ============

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReactionRequest {
    pub message_id: String,
    pub phone: String,
    pub emoji: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RevokeRequest {
    pub message_id: String,
    pub phone: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeleteRequest {
    pub message_id: String,
    pub phone: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateMessageRequest {
    pub message_id: String,
    pub phone: String,
    pub message: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarkAsReadRequest {
    pub message_id: String,
    pub phone: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StarRequest {
    pub message_id: String,
    pub phone: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageActionResponse {
    pub status: String,
    pub message_id: String,
    pub timestamp: i64,
}

// ============ NEWSLETTER TYPES ============

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UnfollowRequest {
    pub newsletter_id: String,
}

// ============ ERROR TYPES ============

#[derive(Debug, thiserror::Error)]
pub enum WhatsAppError {
    #[error("FFI error: {0}")]
    FFI(String),

    #[error("JSON serialization error: {0}")]
    Json(#[from] serde_json::Error),

    #[error("Invalid response: {0}")]
    InvalidResponse(String),

    #[error("WhatsApp API error: {code} - {message}")]
    Api { code: String, message: String },

    #[error("Not initialized")]
    NotInitialized,
}
