use crate::types::*;
use crate::{call_go_function, call_go_function_with_request, parse_response, parse_void_response};
use std::os::raw::c_char;

// External C functions from Go
extern "C" {
    fn app_login() -> *mut c_char;
    fn app_login_with_code(phone: *mut c_char) -> *mut c_char;
    fn app_logout() -> *mut c_char;
    fn app_reconnect() -> *mut c_char;
    fn app_devices() -> *mut c_char;
    fn app_status() -> *mut c_char;
}

/// App API functions
pub struct App;

impl App {
    /// Login to WhatsApp and get QR code
    pub fn login() -> Result<LoginResponse, WhatsAppError> {
        let response_str = call_go_function(app_login)?;
        parse_response(&response_str)
    }

    /// Login with phone number and get pairing code
    pub fn login_with_code(phone: &str) -> Result<LoginWithCodeResponse, WhatsAppError> {
        use std::ffi::CString;

        if !crate::is_initialized() {
            return Err(WhatsAppError::NotInitialized);
        }

        let phone_cstring = CString::new(phone)
            .map_err(|_| WhatsAppError::FFI("Invalid phone string".to_string()))?;

        unsafe {
            let response_ptr = app_login_with_code(phone_cstring.as_ptr() as *mut c_char);
            if response_ptr.is_null() {
                return Err(WhatsAppError::FFI(
                    "Null pointer returned from Go function".to_string(),
                ));
            }

            let response_str = std::ffi::CStr::from_ptr(response_ptr)
                .to_string_lossy()
                .to_string();
            crate::free_string_ptr(response_ptr);

            parse_response(&response_str)
        }
    }

    /// Logout from WhatsApp
    pub fn logout() -> Result<(), WhatsAppError> {
        let response_str = call_go_function(app_logout)?;
        parse_void_response(&response_str)
    }

    /// Reconnect to WhatsApp
    pub fn reconnect() -> Result<(), WhatsAppError> {
        let response_str = call_go_function(app_reconnect)?;
        parse_void_response(&response_str)
    }

    /// Get list of connected devices
    pub fn devices() -> Result<Vec<DeviceInfo>, WhatsAppError> {
        let response_str = call_go_function(app_devices)?;
        parse_response(&response_str)
    }

    /// Get connection status
    pub fn status() -> Result<ConnectionStatus, WhatsAppError> {
        let response_str = call_go_function(app_status)?;
        parse_response(&response_str)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_app_status() {
        // This test would require initialization
        // In a real scenario, you might want to mock the Go functions
        match App::status() {
            Ok(status) => {
                println!("Status: {:?}", status);
            }
            Err(e) => {
                println!("Status failed (expected if not initialized): {}", e);
            }
        }
    }
}
