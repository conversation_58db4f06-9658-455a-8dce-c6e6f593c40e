use crate::types::*;
use crate::{call_go_function_with_request, parse_void_response};
use std::os::raw::c_char;

// External C functions from Go
extern "C" {
    fn newsletter_unfollow(request: *mut c_char) -> *mut c_char;
}

/// Newsletter API functions
pub struct Newsletter;

impl Newsletter {
    /// Unfollow a newsletter
    pub fn unfollow(request: &UnfollowRequest) -> Result<(), WhatsAppError> {
        let response_str = call_go_function_with_request(newsletter_unfollow, request)?;
        parse_void_response(&response_str)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_newsletter_unfollow() {
        let request = UnfollowRequest {
            newsletter_id: "newsletter123".to_string(),
        };

        match Newsletter::unfollow(&request) {
            Ok(_) => {
                println!("Newsletter unfollowed successfully");
            }
            Err(e) => {
                println!("Newsletter unfollow failed (expected if not initialized): {}", e);
            }
        }
    }
}
