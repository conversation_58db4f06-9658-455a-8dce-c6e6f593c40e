use crate::types::*;
use crate::{call_go_function_with_request, parse_response};
use std::os::raw::c_char;

// External C functions from Go
extern "C" {
    fn send_message(request: *mut c_char) -> *mut c_char;
    fn send_image(request: *mut c_char) -> *mut c_char;
    fn send_file(request: *mut c_char) -> *mut c_char;
    fn send_video(request: *mut c_char) -> *mut c_char;
    fn send_contact(request: *mut c_char) -> *mut c_char;
    fn send_link(request: *mut c_char) -> *mut c_char;
    fn send_location(request: *mut c_char) -> *mut c_char;
    fn send_audio(request: *mut c_char) -> *mut c_char;
    fn send_poll(request: *mut c_char) -> *mut c_char;
    fn send_presence(request: *mut c_char) -> *mut c_char;
    fn send_chat_presence(request: *mut c_char) -> *mut c_char;
}

/// Send API functions
pub struct Send;

impl Send {
    /// Send text message
    pub fn message(request: &MessageRequest) -> Result<SendResponse, WhatsAppError> {
        let response_str = call_go_function_with_request(send_message, request)?;
        parse_response(&response_str)
    }

    /// Send image message
    pub fn image(request: &ImageRequest) -> Result<SendResponse, WhatsAppError> {
        let response_str = call_go_function_with_request(send_image, request)?;
        parse_response(&response_str)
    }

    /// Send file message
    pub fn file(request: &FileRequest) -> Result<SendResponse, WhatsAppError> {
        let response_str = call_go_function_with_request(send_file, request)?;
        parse_response(&response_str)
    }

    /// Send video message
    pub fn video(request: &VideoRequest) -> Result<SendResponse, WhatsAppError> {
        let response_str = call_go_function_with_request(send_video, request)?;
        parse_response(&response_str)
    }

    /// Send contact message
    pub fn contact(request: &ContactRequest) -> Result<SendResponse, WhatsAppError> {
        let response_str = call_go_function_with_request(send_contact, request)?;
        parse_response(&response_str)
    }

    /// Send link message
    pub fn link(request: &LinkRequest) -> Result<SendResponse, WhatsAppError> {
        let response_str = call_go_function_with_request(send_link, request)?;
        parse_response(&response_str)
    }

    /// Send location message
    pub fn location(request: &LocationRequest) -> Result<SendResponse, WhatsAppError> {
        let response_str = call_go_function_with_request(send_location, request)?;
        parse_response(&response_str)
    }

    /// Send audio message
    pub fn audio(request: &AudioRequest) -> Result<SendResponse, WhatsAppError> {
        let response_str = call_go_function_with_request(send_audio, request)?;
        parse_response(&response_str)
    }

    /// Send poll message
    pub fn poll(request: &PollRequest) -> Result<SendResponse, WhatsAppError> {
        let response_str = call_go_function_with_request(send_poll, request)?;
        parse_response(&response_str)
    }

    /// Send presence status
    pub fn presence(request: &PresenceRequest) -> Result<SendResponse, WhatsAppError> {
        let response_str = call_go_function_with_request(send_presence, request)?;
        parse_response(&response_str)
    }

    /// Send chat presence (typing indicator)
    pub fn chat_presence(request: &ChatPresenceRequest) -> Result<SendResponse, WhatsAppError> {
        let response_str = call_go_function_with_request(send_chat_presence, request)?;
        parse_response(&response_str)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_send_message() {
        let request = MessageRequest {
            phone: "1234567890".to_string(),
            message: "Hello, World!".to_string(),
            reply_message_id: None,
            duration: None,
            is_forwarded: None,
        };

        match Send::message(&request) {
            Ok(response) => {
                println!("Message sent: {:?}", response);
            }
            Err(e) => {
                println!("Send failed (expected if not initialized): {}", e);
            }
        }
    }

    #[test]
    fn test_send_presence() {
        let request = PresenceRequest {
            state: "available".to_string(),
        };

        match Send::presence(&request) {
            Ok(response) => {
                println!("Presence sent: {:?}", response);
            }
            Err(e) => {
                println!("Presence failed (expected if not initialized): {}", e);
            }
        }
    }
}
