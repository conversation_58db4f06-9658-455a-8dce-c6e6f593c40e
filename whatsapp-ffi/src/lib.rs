pub mod types;
pub mod app;
pub mod send;
pub mod user;
pub mod chat;
pub mod group;
pub mod message;
pub mod newsletter;

#[cfg(test)]
mod tests;

use std::ffi::{CStr, CString};
use std::os::raw::c_char;
use std::sync::Once;

pub use types::*;

static INIT: Once = Once::new();
static mut INITIALIZED: bool = false;

// External C functions from Go
extern "C" {
    fn whatsapp_init() -> *mut c_char;
    fn free_string(ptr: *mut c_char);
}

// Re-export the free_string function for use in other modules
pub unsafe fn free_string_ptr(ptr: *mut c_char) {
    free_string(ptr);
}

/// Initialize the WhatsApp client
pub fn init() -> Result<(), WhatsAppError> {
    let mut result = Err(WhatsAppError::NotInitialized);
    
    INIT.call_once(|| {
        unsafe {
            let response_ptr = whatsapp_init();
            if response_ptr.is_null() {
                return;
            }
            
            let response_str = CStr::from_ptr(response_ptr).to_string_lossy();
            free_string(response_ptr);
            
            match serde_json::from_str::<FFIResponse<()>>(&response_str) {
                Ok(response) => {
                    if response.status == 200 {
                        INITIALIZED = true;
                        result = Ok(());
                    } else {
                        result = Err(WhatsAppError::Api {
                            code: response.code,
                            message: response.message,
                        });
                    }
                }
                Err(e) => {
                    result = Err(WhatsAppError::Json(e));
                }
            }
        });
    });
    
    result
}

/// Check if the WhatsApp client is initialized
pub fn is_initialized() -> bool {
    unsafe { INITIALIZED }
}

// Helper function to call Go functions with JSON
fn call_go_function(func: unsafe extern "C" fn() -> *mut c_char) -> Result<String, WhatsAppError> {
    if !is_initialized() {
        return Err(WhatsAppError::NotInitialized);
    }
    
    unsafe {
        let response_ptr = func();
        if response_ptr.is_null() {
            return Err(WhatsAppError::FFI("Null pointer returned from Go function".to_string()));
        }
        
        let response_str = CStr::from_ptr(response_ptr).to_string_lossy().to_string();
        free_string(response_ptr);
        
        Ok(response_str)
    }
}

// Helper function to call Go functions with JSON request
fn call_go_function_with_request<T: serde::Serialize>(
    func: unsafe extern "C" fn(*mut c_char) -> *mut c_char,
    request: &T,
) -> Result<String, WhatsAppError> {
    if !is_initialized() {
        return Err(WhatsAppError::NotInitialized);
    }
    
    let request_json = serde_json::to_string(request)?;
    let request_cstring = CString::new(request_json)
        .map_err(|_| WhatsAppError::FFI("Invalid request string".to_string()))?;
    
    unsafe {
        let response_ptr = func(request_cstring.as_ptr() as *mut c_char);
        if response_ptr.is_null() {
            return Err(WhatsAppError::FFI("Null pointer returned from Go function".to_string()));
        }
        
        let response_str = CStr::from_ptr(response_ptr).to_string_lossy().to_string();
        free_string(response_ptr);
        
        Ok(response_str)
    }
}

// Helper function to parse response
fn parse_response<T: for<'de> serde::Deserialize<'de>>(response_str: &str) -> Result<T, WhatsAppError> {
    let response: FFIResponse<T> = serde_json::from_str(response_str)?;
    
    if response.status == 200 {
        response.results.ok_or_else(|| {
            WhatsAppError::InvalidResponse("Missing results in successful response".to_string())
        })
    } else {
        Err(WhatsAppError::Api {
            code: response.code,
            message: response.message,
        })
    }
}

// Helper function for void responses
fn parse_void_response(response_str: &str) -> Result<(), WhatsAppError> {
    let response: FFIResponse<()> = serde_json::from_str(response_str)?;
    
    if response.status == 200 {
        Ok(())
    } else {
        Err(WhatsAppError::Api {
            code: response.code,
            message: response.message,
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_init() {
        // Note: This test requires Go dependencies to be available
        // In a real scenario, you might want to mock the Go functions
        match init() {
            Ok(_) => {
                assert!(is_initialized());
            }
            Err(e) => {
                // It's okay if init fails in test environment
                println!("Init failed (expected in test): {}", e);
            }
        }
    }
}
