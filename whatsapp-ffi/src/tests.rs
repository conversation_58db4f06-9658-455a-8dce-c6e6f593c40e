#[cfg(test)]
mod tests {
    use super::types::*;
    use serde_json;

    #[test]
    fn test_message_request_serialization() {
        let request = MessageRequest {
            phone: "1234567890".to_string(),
            message: "Hello, <PERSON>!".to_string(),
            reply_message_id: None,
            duration: None,
            is_forwarded: None,
        };

        let json = serde_json::to_string(&request).unwrap();
        let deserialized: MessageRequest = serde_json::from_str(&json).unwrap();
        
        assert_eq!(request.phone, deserialized.phone);
        assert_eq!(request.message, deserialized.message);
    }

    #[test]
    fn test_image_request_serialization() {
        let request = ImageRequest {
            phone: "1234567890".to_string(),
            image_url: Some("https://example.com/image.jpg".to_string()),
            caption: Some("Test image".to_string()),
            reply_message_id: None,
            compress: Some(true),
            duration: None,
            is_forwarded: None,
        };

        let json = serde_json::to_string(&request).unwrap();
        let deserialized: ImageRequest = serde_json::from_str(&json).unwrap();
        
        assert_eq!(request.phone, deserialized.phone);
        assert_eq!(request.image_url, deserialized.image_url);
        assert_eq!(request.caption, deserialized.caption);
        assert_eq!(request.compress, deserialized.compress);
    }

    #[test]
    fn test_group_create_request_serialization() {
        let request = CreateGroupRequest {
            name: "Test Group".to_string(),
            participants: vec!["1234567890".to_string(), "0987654321".to_string()],
        };

        let json = serde_json::to_string(&request).unwrap();
        let deserialized: CreateGroupRequest = serde_json::from_str(&json).unwrap();
        
        assert_eq!(request.name, deserialized.name);
        assert_eq!(request.participants, deserialized.participants);
    }

    #[test]
    fn test_ffi_response_serialization() {
        let response = FFIResponse {
            status: 200,
            code: "SUCCESS".to_string(),
            message: "Operation completed".to_string(),
            results: Some("test result".to_string()),
            error: None,
        };

        let json = serde_json::to_string(&response).unwrap();
        let deserialized: FFIResponse<String> = serde_json::from_str(&json).unwrap();
        
        assert_eq!(response.status, deserialized.status);
        assert_eq!(response.code, deserialized.code);
        assert_eq!(response.message, deserialized.message);
        assert_eq!(response.results, deserialized.results);
    }

    #[test]
    fn test_connection_status_serialization() {
        let status = ConnectionStatus {
            is_connected: true,
            is_logged_in: false,
            device_id: "device123".to_string(),
        };

        let json = serde_json::to_string(&status).unwrap();
        let deserialized: ConnectionStatus = serde_json::from_str(&json).unwrap();
        
        assert_eq!(status.is_connected, deserialized.is_connected);
        assert_eq!(status.is_logged_in, deserialized.is_logged_in);
        assert_eq!(status.device_id, deserialized.device_id);
    }

    #[test]
    fn test_poll_request_serialization() {
        let request = PollRequest {
            phone: "1234567890".to_string(),
            question: "What's your favorite color?".to_string(),
            options: vec!["Red".to_string(), "Blue".to_string(), "Green".to_string()],
            selectable_count: Some(1),
            reply_message_id: None,
            duration: None,
            is_forwarded: None,
        };

        let json = serde_json::to_string(&request).unwrap();
        let deserialized: PollRequest = serde_json::from_str(&json).unwrap();
        
        assert_eq!(request.phone, deserialized.phone);
        assert_eq!(request.question, deserialized.question);
        assert_eq!(request.options, deserialized.options);
        assert_eq!(request.selectable_count, deserialized.selectable_count);
    }

    #[test]
    fn test_location_request_serialization() {
        let request = LocationRequest {
            phone: "1234567890".to_string(),
            latitude: 37.7749,
            longitude: -122.4194,
            name: Some("San Francisco".to_string()),
            address: Some("San Francisco, CA, USA".to_string()),
            reply_message_id: None,
            duration: None,
            is_forwarded: None,
        };

        let json = serde_json::to_string(&request).unwrap();
        let deserialized: LocationRequest = serde_json::from_str(&json).unwrap();
        
        assert_eq!(request.phone, deserialized.phone);
        assert!((request.latitude - deserialized.latitude).abs() < f64::EPSILON);
        assert!((request.longitude - deserialized.longitude).abs() < f64::EPSILON);
        assert_eq!(request.name, deserialized.name);
        assert_eq!(request.address, deserialized.address);
    }

    #[test]
    fn test_whatsapp_error_display() {
        let error = WhatsAppError::NotInitialized;
        assert_eq!(format!("{}", error), "Not initialized");

        let error = WhatsAppError::Api {
            code: "ERROR_CODE".to_string(),
            message: "Something went wrong".to_string(),
        };
        assert_eq!(format!("{}", error), "WhatsApp API error: ERROR_CODE - Something went wrong");

        let error = WhatsAppError::FFI("FFI call failed".to_string());
        assert_eq!(format!("{}", error), "FFI error: FFI call failed");
    }

    #[test]
    fn test_check_user_request_serialization() {
        let request = CheckUserRequest {
            phones: vec!["1234567890".to_string(), "0987654321".to_string(), "1122334455".to_string()],
        };

        let json = serde_json::to_string(&request).unwrap();
        let deserialized: CheckUserRequest = serde_json::from_str(&json).unwrap();
        
        assert_eq!(request.phones, deserialized.phones);
    }

    #[test]
    fn test_reaction_request_serialization() {
        let request = ReactionRequest {
            message_id: "msg123".to_string(),
            phone: "1234567890".to_string(),
            emoji: "👍".to_string(),
        };

        let json = serde_json::to_string(&request).unwrap();
        let deserialized: ReactionRequest = serde_json::from_str(&json).unwrap();
        
        assert_eq!(request.message_id, deserialized.message_id);
        assert_eq!(request.phone, deserialized.phone);
        assert_eq!(request.emoji, deserialized.emoji);
    }
}
