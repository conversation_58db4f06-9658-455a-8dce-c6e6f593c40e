use crate::types::*;
use crate::{call_go_function_with_request, parse_response};
use std::os::raw::c_char;

// External C functions from Go
extern "C" {
    fn chat_list(request: *mut c_char) -> *mut c_char;
    fn chat_messages(request: *mut c_char) -> *mut c_char;
    fn chat_pin(request: *mut c_char) -> *mut c_char;
}

/// Chat API functions
pub struct Chat;

impl Chat {
    /// Get list of chats
    pub fn list(request: &ListChatsRequest) -> Result<Vec<ChatInfo>, WhatsAppError> {
        let response_str = call_go_function_with_request(chat_list, request)?;
        parse_response(&response_str)
    }

    /// Get messages from a chat
    pub fn messages(request: &GetChatMessagesRequest) -> Result<Vec<MessageInfo>, WhatsAppError> {
        let response_str = call_go_function_with_request(chat_messages, request)?;
        parse_response(&response_str)
    }

    /// Pin or unpin a chat
    pub fn pin(request: &PinChatRequest) -> Result<PinChatResponse, WhatsAppError> {
        let response_str = call_go_function_with_request(chat_pin, request)?;
        parse_response(&response_str)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_chat_list() {
        let request = ListChatsRequest {
            limit: Some(10),
            offset: Some(0),
            search: None,
            has_media: None,
        };

        match Chat::list(&request) {
            Ok(chats) => {
                println!("Chats: {:?}", chats);
            }
            Err(e) => {
                println!("Chat list failed (expected if not initialized): {}", e);
            }
        }
    }

    #[test]
    fn test_chat_messages() {
        let request = GetChatMessagesRequest {
            chat_jid: "<EMAIL>".to_string(),
            limit: Some(10),
            offset: Some(0),
            media_only: None,
            search: None,
            start_time: None,
            end_time: None,
            is_from_me: None,
        };

        match Chat::messages(&request) {
            Ok(messages) => {
                println!("Messages: {:?}", messages);
            }
            Err(e) => {
                println!("Chat messages failed (expected if not initialized): {}", e);
            }
        }
    }

    #[test]
    fn test_chat_pin() {
        let request = PinChatRequest {
            chat_jid: "<EMAIL>".to_string(),
            pin: true,
        };

        match Chat::pin(&request) {
            Ok(response) => {
                println!("Pin response: {:?}", response);
            }
            Err(e) => {
                println!("Chat pin failed (expected if not initialized): {}", e);
            }
        }
    }
}
