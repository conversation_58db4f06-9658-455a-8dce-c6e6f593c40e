use crate::types::*;
use crate::{call_go_function_with_request, parse_response, parse_void_response};
use std::os::raw::c_char;

// External C functions from Go
extern "C" {
    fn group_create(request: *mut c_char) -> *mut c_char;
    fn group_join_with_link(request: *mut c_char) -> *mut c_char;
    fn group_info_from_link(request: *mut c_char) -> *mut c_char;
    fn group_info(request: *mut c_char) -> *mut c_char;
    fn group_leave(request: *mut c_char) -> *mut c_char;
    fn group_add_participants(request: *mut c_char) -> *mut c_char;
    fn group_remove_participants(request: *mut c_char) -> *mut c_char;
    fn group_promote_participants(request: *mut c_char) -> *mut c_char;
    fn group_demote_participants(request: *mut c_char) -> *mut c_char;
    fn group_list_participant_requests(request: *mut c_char) -> *mut c_char;
    fn group_approve_participant_requests(request: *mut c_char) -> *mut c_char;
    fn group_reject_participant_requests(request: *mut c_char) -> *mut c_char;
    fn group_set_photo(request: *mut c_char) -> *mut c_char;
    fn group_set_name(request: *mut c_char) -> *mut c_char;
    fn group_set_locked(request: *mut c_char) -> *mut c_char;
    fn group_set_announce(request: *mut c_char) -> *mut c_char;
    fn group_set_topic(request: *mut c_char) -> *mut c_char;
}

/// Group API functions
pub struct Group;

impl Group {
    /// Create a new group
    pub fn create(request: &CreateGroupRequest) -> Result<CreateGroupResponse, WhatsAppError> {
        let response_str = call_go_function_with_request(group_create, request)?;
        parse_response(&response_str)
    }

    /// Join group with invitation link
    pub fn join_with_link(request: &JoinGroupWithLinkRequest) -> Result<CreateGroupResponse, WhatsAppError> {
        let response_str = call_go_function_with_request(group_join_with_link, request)?;
        parse_response(&response_str)
    }

    /// Get group info from invitation link
    pub fn info_from_link(request: &GetGroupInfoFromLinkRequest) -> Result<GroupInfo, WhatsAppError> {
        let response_str = call_go_function_with_request(group_info_from_link, request)?;
        parse_response(&response_str)
    }

    /// Get group information
    pub fn info(request: &GroupInfoRequest) -> Result<GroupInfo, WhatsAppError> {
        let response_str = call_go_function_with_request(group_info, request)?;
        parse_response(&response_str)
    }

    /// Leave a group
    pub fn leave(request: &LeaveGroupRequest) -> Result<(), WhatsAppError> {
        let response_str = call_go_function_with_request(group_leave, request)?;
        parse_void_response(&response_str)
    }

    /// Add participants to group
    pub fn add_participants(request: &ParticipantRequest) -> Result<serde_json::Value, WhatsAppError> {
        let response_str = call_go_function_with_request(group_add_participants, request)?;
        parse_response(&response_str)
    }

    /// Remove participants from group
    pub fn remove_participants(request: &ParticipantRequest) -> Result<serde_json::Value, WhatsAppError> {
        let response_str = call_go_function_with_request(group_remove_participants, request)?;
        parse_response(&response_str)
    }

    /// Promote participants to admin
    pub fn promote_participants(request: &ParticipantRequest) -> Result<serde_json::Value, WhatsAppError> {
        let response_str = call_go_function_with_request(group_promote_participants, request)?;
        parse_response(&response_str)
    }

    /// Demote participants from admin
    pub fn demote_participants(request: &ParticipantRequest) -> Result<serde_json::Value, WhatsAppError> {
        let response_str = call_go_function_with_request(group_demote_participants, request)?;
        parse_response(&response_str)
    }

    /// List participant requests
    pub fn list_participant_requests(request: &GetGroupRequestParticipantsRequest) -> Result<serde_json::Value, WhatsAppError> {
        let response_str = call_go_function_with_request(group_list_participant_requests, request)?;
        parse_response(&response_str)
    }

    /// Approve participant requests
    pub fn approve_participant_requests(request: &GroupRequestParticipantsRequest) -> Result<serde_json::Value, WhatsAppError> {
        let response_str = call_go_function_with_request(group_approve_participant_requests, request)?;
        parse_response(&response_str)
    }

    /// Reject participant requests
    pub fn reject_participant_requests(request: &GroupRequestParticipantsRequest) -> Result<serde_json::Value, WhatsAppError> {
        let response_str = call_go_function_with_request(group_reject_participant_requests, request)?;
        parse_response(&response_str)
    }

    /// Set group photo
    pub fn set_photo(request: &SetGroupPhotoRequest) -> Result<SetGroupPhotoResponse, WhatsAppError> {
        let response_str = call_go_function_with_request(group_set_photo, request)?;
        parse_response(&response_str)
    }

    /// Set group name
    pub fn set_name(request: &SetGroupNameRequest) -> Result<(), WhatsAppError> {
        let response_str = call_go_function_with_request(group_set_name, request)?;
        parse_void_response(&response_str)
    }

    /// Set group locked status
    pub fn set_locked(request: &SetGroupLockedRequest) -> Result<(), WhatsAppError> {
        let response_str = call_go_function_with_request(group_set_locked, request)?;
        parse_void_response(&response_str)
    }

    /// Set group announce mode
    pub fn set_announce(request: &SetGroupAnnounceRequest) -> Result<(), WhatsAppError> {
        let response_str = call_go_function_with_request(group_set_announce, request)?;
        parse_void_response(&response_str)
    }

    /// Set group topic
    pub fn set_topic(request: &SetGroupTopicRequest) -> Result<(), WhatsAppError> {
        let response_str = call_go_function_with_request(group_set_topic, request)?;
        parse_void_response(&response_str)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_group_create() {
        let request = CreateGroupRequest {
            name: "Test Group".to_string(),
            participants: vec!["1234567890".to_string(), "0987654321".to_string()],
        };

        match Group::create(&request) {
            Ok(response) => {
                println!("Group created: {:?}", response);
            }
            Err(e) => {
                println!("Group create failed (expected if not initialized): {}", e);
            }
        }
    }

    #[test]
    fn test_group_info() {
        let request = GroupInfoRequest {
            group_id: "<EMAIL>".to_string(),
        };

        match Group::info(&request) {
            Ok(info) => {
                println!("Group info: {:?}", info);
            }
            Err(e) => {
                println!("Group info failed (expected if not initialized): {}", e);
            }
        }
    }
}
