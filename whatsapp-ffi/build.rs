use std::env;
use std::path::PathBuf;
use std::process::Command;

fn main() {
    let out_dir = env::var("OUT_DIR").unwrap();
    let go_dir = PathBuf::from("go");
    
    // Build the Go shared library
    let output = Command::new("go")
        .args(&[
            "build",
            "-buildmode=c-archive",
            "-o",
            &format!("{}/libwhatsapp.a", out_dir),
            "./ffi.go",
        ])
        .current_dir(&go_dir)
        .output()
        .expect("Failed to execute go build command");

    if !output.status.success() {
        panic!(
            "Go build failed: {}",
            String::from_utf8_lossy(&output.stderr)
        );
    }

    // Tell cargo to link the library
    println!("cargo:rustc-link-search=native={}", out_dir);
    println!("cargo:rustc-link-lib=static=whatsapp");
    
    // Tell cargo to rerun if Go files change
    println!("cargo:rerun-if-changed=go/ffi.go");
    println!("cargo:rerun-if-changed=go/go.mod");
    println!("cargo:rerun-if-changed=go/go.sum");
}
