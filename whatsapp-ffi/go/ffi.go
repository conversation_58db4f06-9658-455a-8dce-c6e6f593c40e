package main

/*
#include <stdlib.h>
*/
import "C"
import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"unsafe"

	"github.com/aldinokemal/go-whatsapp-web-multidevice/config"
	domainApp "github.com/aldinokemal/go-whatsapp-web-multidevice/domains/app"
	domainChat "github.com/aldinokemal/go-whatsapp-web-multidevice/domains/chat"
	domainGroup "github.com/aldinokemal/go-whatsapp-web-multidevice/domains/group"
	domainMessage "github.com/aldinokemal/go-whatsapp-web-multidevice/domains/message"
	domainNewsletter "github.com/aldinokemal/go-whatsapp-web-multidevice/domains/newsletter"
	domainSend "github.com/aldinokemal/go-whatsapp-web-multidevice/domains/send"
	domainUser "github.com/aldinokemal/go-whatsapp-web-multidevice/domains/user"
	"github.com/aldinokemal/go-whatsapp-web-multidevice/infrastructure/whatsapp"
	"github.com/aldinokemal/go-whatsapp-web-multidevice/usecase"
)

// Global services
var (
	appService        domainApp.IAppUsecase
	sendService       domainSend.ISendUsecase
	userService       domainUser.IUserUsecase
	chatService       domainChat.IChatUsecase
	groupService      domainGroup.IGroupUsecase
	messageService    domainMessage.IMessageUsecase
	newsletterService domainNewsletter.INewsletterUsecase
)

// Response structure for FFI
type FFIResponse struct {
	Status  int         `json:"status"`
	Code    string      `json:"code"`
	Message string      `json:"message"`
	Results interface{} `json:"results,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// Helper function to convert Go string to C string
func goStringToCString(s string) *C.char {
	return C.CString(s)
}

// Helper function to convert C string to Go string
func cStringToGoString(s *C.char) string {
	return C.GoString(s)
}

// Helper function to create JSON response
func createResponse(status int, code, message string, results interface{}, err error) *C.char {
	response := FFIResponse{
		Status:  status,
		Code:    code,
		Message: message,
		Results: results,
	}
	
	if err != nil {
		response.Error = err.Error()
	}
	
	jsonData, _ := json.Marshal(response)
	return goStringToCString(string(jsonData))
}

//export whatsapp_init
func whatsapp_init() *C.char {
	// Initialize configuration
	config.LoadConfig()
	
	// Initialize WhatsApp client
	err := whatsapp.InitWaClient()
	if err != nil {
		return createResponse(500, "INIT_ERROR", "Failed to initialize WhatsApp client", nil, err)
	}
	
	// Initialize services
	appService = usecase.NewAppUsecase()
	sendService = usecase.NewSendUsecase()
	userService = usecase.NewUserUsecase()
	chatService = usecase.NewChatUsecase()
	groupService = usecase.NewGroupUsecase()
	messageService = usecase.NewMessageUsecase()
	newsletterService = usecase.NewNewsletterUsecase()
	
	return createResponse(200, "SUCCESS", "WhatsApp client initialized successfully", nil, nil)
}

// ============ APP APIs ============

//export app_login
func app_login() *C.char {
	ctx := context.Background()
	response, err := appService.Login(ctx)
	if err != nil {
		return createResponse(500, "ERROR", "Login failed", nil, err)
	}
	
	result := map[string]interface{}{
		"qr_link":     fmt.Sprintf("http://localhost:3000%s/%s", config.AppBasePath, response.ImagePath),
		"qr_duration": response.Duration,
	}
	
	return createResponse(200, "SUCCESS", "Login success", result, nil)
}

//export app_login_with_code
func app_login_with_code(phone *C.char) *C.char {
	ctx := context.Background()
	phoneStr := cStringToGoString(phone)
	
	pairCode, err := appService.LoginWithCode(ctx, phoneStr)
	if err != nil {
		return createResponse(500, "ERROR", "Login with code failed", nil, err)
	}
	
	result := map[string]interface{}{
		"pair_code": pairCode,
	}
	
	return createResponse(200, "SUCCESS", "Login with code success", result, nil)
}

//export app_logout
func app_logout() *C.char {
	ctx := context.Background()
	err := appService.Logout(ctx)
	if err != nil {
		return createResponse(500, "ERROR", "Logout failed", nil, err)
	}
	
	return createResponse(200, "SUCCESS", "Success logout", nil, nil)
}

//export app_reconnect
func app_reconnect() *C.char {
	ctx := context.Background()
	err := appService.Reconnect(ctx)
	if err != nil {
		return createResponse(500, "ERROR", "Reconnect failed", nil, err)
	}
	
	return createResponse(200, "SUCCESS", "Reconnect success", nil, nil)
}

//export app_devices
func app_devices() *C.char {
	ctx := context.Background()
	devices, err := appService.FetchDevices(ctx)
	if err != nil {
		return createResponse(500, "ERROR", "Fetch devices failed", nil, err)
	}
	
	return createResponse(200, "SUCCESS", "Fetch device success", devices, nil)
}

//export app_status
func app_status() *C.char {
	isConnected, isLoggedIn, deviceID := whatsapp.GetConnectionStatus()
	
	result := map[string]interface{}{
		"is_connected": isConnected,
		"is_logged_in": isLoggedIn,
		"device_id":    deviceID,
	}
	
	return createResponse(200, "SUCCESS", "Connection status retrieved", result, nil)
}

// ============ SEND APIs ============

//export send_message
func send_message(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)
	
	var request domainSend.MessageRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}
	
	response, err := sendService.SendText(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Send message failed", nil, err)
	}
	
	return createResponse(200, "SUCCESS", response.Status, response, nil)
}

//export send_image
func send_image(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)
	
	var request domainSend.ImageRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}
	
	response, err := sendService.SendImage(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Send image failed", nil, err)
	}
	
	return createResponse(200, "SUCCESS", response.Status, response, nil)
}

//export send_file
func send_file(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)
	
	var request domainSend.FileRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}
	
	response, err := sendService.SendFile(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Send file failed", nil, err)
	}
	
	return createResponse(200, "SUCCESS", response.Status, response, nil)
}

//export send_video
func send_video(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)
	
	var request domainSend.VideoRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}
	
	response, err := sendService.SendVideo(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Send video failed", nil, err)
	}
	
	return createResponse(200, "SUCCESS", response.Status, response, nil)
}

//export send_contact
func send_contact(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)
	
	var request domainSend.ContactRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}
	
	response, err := sendService.SendContact(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Send contact failed", nil, err)
	}
	
	return createResponse(200, "SUCCESS", response.Status, response, nil)
}

//export send_link
func send_link(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)
	
	var request domainSend.LinkRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}
	
	response, err := sendService.SendLink(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Send link failed", nil, err)
	}
	
	return createResponse(200, "SUCCESS", response.Status, response, nil)
}

//export send_location
func send_location(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)
	
	var request domainSend.LocationRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}
	
	response, err := sendService.SendLocation(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Send location failed", nil, err)
	}
	
	return createResponse(200, "SUCCESS", response.Status, response, nil)
}

//export send_audio
func send_audio(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)
	
	var request domainSend.AudioRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}
	
	response, err := sendService.SendAudio(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Send audio failed", nil, err)
	}
	
	return createResponse(200, "SUCCESS", response.Status, response, nil)
}

//export send_poll
func send_poll(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)
	
	var request domainSend.PollRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}
	
	response, err := sendService.SendPoll(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Send poll failed", nil, err)
	}
	
	return createResponse(200, "SUCCESS", response.Status, response, nil)
}

//export send_presence
func send_presence(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)
	
	var request domainSend.PresenceRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}
	
	response, err := sendService.SendPresence(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Send presence failed", nil, err)
	}
	
	return createResponse(200, "SUCCESS", response.Status, response, nil)
}

//export send_chat_presence
func send_chat_presence(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)
	
	var request domainSend.ChatPresenceRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}
	
	response, err := sendService.SendChatPresence(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Send chat presence failed", nil, err)
	}
	
	return createResponse(200, "SUCCESS", response.Status, response, nil)
}

// ============ USER APIs ============

//export user_info
func user_info(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainUser.InfoRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	response, err := userService.Info(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Get user info failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Success get user info", response.Data[0], nil)
}

//export user_avatar
func user_avatar(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainUser.AvatarRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	response, err := userService.Avatar(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Get avatar failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Success get avatar", response, nil)
}

//export user_change_avatar
func user_change_avatar(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainUser.ChangeAvatarRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	err := userService.ChangeAvatar(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Change avatar failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Success change avatar", nil, nil)
}

//export user_change_pushname
func user_change_pushname(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainUser.ChangePushNameRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	err := userService.ChangePushName(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Change push name failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Success change push name", nil, nil)
}

//export user_my_privacy
func user_my_privacy() *C.char {
	ctx := context.Background()

	response, err := userService.MyPrivacySetting(ctx)
	if err != nil {
		return createResponse(500, "ERROR", "Get privacy failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Success get privacy", response, nil)
}

//export user_my_groups
func user_my_groups() *C.char {
	ctx := context.Background()

	response, err := userService.MyListGroups(ctx)
	if err != nil {
		return createResponse(500, "ERROR", "Get list groups failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Success get list groups", response, nil)
}

//export user_my_newsletters
func user_my_newsletters() *C.char {
	ctx := context.Background()

	response, err := userService.MyListNewsletter(ctx)
	if err != nil {
		return createResponse(500, "ERROR", "Get list newsletter failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Success get list newsletter", response, nil)
}

//export user_my_contacts
func user_my_contacts() *C.char {
	ctx := context.Background()

	response, err := userService.MyListContacts(ctx)
	if err != nil {
		return createResponse(500, "ERROR", "Get list contacts failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Success get list contacts", response, nil)
}

//export user_check
func user_check(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainUser.CheckRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	response, err := userService.IsOnWhatsApp(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Check user failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Success check user", response, nil)
}

//export user_business_profile
func user_business_profile(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainUser.BusinessProfileRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	response, err := userService.BusinessProfile(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Get business profile failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Success get business profile", response, nil)
}

// ============ CHAT APIs ============

//export chat_list
func chat_list(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainChat.ListChatsRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	response, err := chatService.ListChats(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Get chat list failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Success get chat list", response, nil)
}

//export chat_messages
func chat_messages(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainChat.GetChatMessagesRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	response, err := chatService.GetChatMessages(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Get chat messages failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Success get chat messages", response, nil)
}

//export chat_pin
func chat_pin(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainChat.PinChatRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	response, err := chatService.PinChat(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Pin chat failed", nil, err)
	}

	return createResponse(200, "SUCCESS", response.Message, response, nil)
}

// ============ GROUP APIs ============

//export group_create
func group_create(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainGroup.CreateGroupRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	groupID, err := groupService.CreateGroup(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Create group failed", nil, err)
	}

	result := map[string]string{
		"group_id": groupID,
	}

	return createResponse(200, "SUCCESS", fmt.Sprintf("Success created group with id %s", groupID), result, nil)
}

//export group_join_with_link
func group_join_with_link(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainGroup.JoinGroupWithLinkRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	response, err := groupService.JoinGroupWithLink(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Join group failed", nil, err)
	}

	result := map[string]string{
		"group_id": response,
	}

	return createResponse(200, "SUCCESS", "Success joined group", result, nil)
}

//export group_info_from_link
func group_info_from_link(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainGroup.GetGroupInfoFromLinkRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	response, err := groupService.GetGroupInfoFromLink(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Get group info from link failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Success get group info from link", response, nil)
}

//export group_info
func group_info(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainGroup.GroupInfoRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	response, err := groupService.GroupInfo(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Get group info failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Success get group info", response.Data, nil)
}

//export group_leave
func group_leave(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainGroup.LeaveGroupRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	err := groupService.LeaveGroup(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Leave group failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Success leave group", nil, nil)
}

//export group_add_participants
func group_add_participants(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainGroup.ParticipantRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	result, err := groupService.ManageParticipant(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Add participants failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Success add participants", result, nil)
}

//export group_remove_participants
func group_remove_participants(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainGroup.ParticipantRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	result, err := groupService.ManageParticipant(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Remove participants failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Success remove participants", result, nil)
}

//export group_promote_participants
func group_promote_participants(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainGroup.ParticipantRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	result, err := groupService.ManageParticipant(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Promote participants failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Success promote participants", result, nil)
}

//export group_demote_participants
func group_demote_participants(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainGroup.ParticipantRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	result, err := groupService.ManageParticipant(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Demote participants failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Success demote participants", result, nil)
}

//export group_list_participant_requests
func group_list_participant_requests(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainGroup.GetGroupRequestParticipantsRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	result, err := groupService.GetGroupRequestParticipants(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Get participant requests failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Success getting list requested participants", result, nil)
}

//export group_approve_participant_requests
func group_approve_participant_requests(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainGroup.GroupRequestParticipantsRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	result, err := groupService.ManageGroupRequestParticipants(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Approve participant requests failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Success approve requested participants", result, nil)
}

//export group_reject_participant_requests
func group_reject_participant_requests(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainGroup.GroupRequestParticipantsRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	result, err := groupService.ManageGroupRequestParticipants(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Reject participant requests failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Success reject requested participants", result, nil)
}

//export group_set_photo
func group_set_photo(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainGroup.SetGroupPhotoRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	pictureID, err := groupService.SetGroupPhoto(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Set group photo failed", nil, err)
	}

	message := "Success update group photo"
	if request.Photo == nil {
		message = "Success remove group photo"
	}

	result := domainGroup.SetGroupPhotoResponse{
		PictureID: pictureID,
		Message:   message,
	}

	return createResponse(200, "SUCCESS", message, result, nil)
}

//export group_set_name
func group_set_name(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainGroup.SetGroupNameRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	err := groupService.SetGroupName(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Set group name failed", nil, err)
	}

	message := fmt.Sprintf("Success update group name to '%s'", request.Name)
	return createResponse(200, "SUCCESS", message, nil, nil)
}

//export group_set_locked
func group_set_locked(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainGroup.SetGroupLockedRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	err := groupService.SetGroupLocked(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Set group locked failed", nil, err)
	}

	message := "Success set group as unlocked"
	if request.Locked {
		message = "Success set group as locked"
	}

	return createResponse(200, "SUCCESS", message, nil, nil)
}

//export group_set_announce
func group_set_announce(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainGroup.SetGroupAnnounceRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	err := groupService.SetGroupAnnounce(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Set group announce failed", nil, err)
	}

	message := "Success disable announce mode"
	if request.Announce {
		message = "Success enable announce mode"
	}

	return createResponse(200, "SUCCESS", message, nil, nil)
}

//export group_set_topic
func group_set_topic(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainGroup.SetGroupTopicRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	err := groupService.SetGroupTopic(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Set group topic failed", nil, err)
	}

	message := "Success update group topic"
	if request.Topic == "" {
		message = "Success remove group topic"
	}

	return createResponse(200, "SUCCESS", message, nil, nil)
}

// ============ MESSAGE APIs ============

//export message_react
func message_react(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainMessage.ReactionRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	response, err := messageService.ReactMessage(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "React message failed", nil, err)
	}

	return createResponse(200, "SUCCESS", response.Status, response, nil)
}

//export message_revoke
func message_revoke(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainMessage.RevokeRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	response, err := messageService.RevokeMessage(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Revoke message failed", nil, err)
	}

	return createResponse(200, "SUCCESS", response.Status, response, nil)
}

//export message_delete
func message_delete(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainMessage.DeleteRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	err := messageService.DeleteMessage(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Delete message failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Message deleted successfully", nil, nil)
}

//export message_update
func message_update(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainMessage.UpdateMessageRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	response, err := messageService.UpdateMessage(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Update message failed", nil, err)
	}

	return createResponse(200, "SUCCESS", response.Status, response, nil)
}

//export message_mark_as_read
func message_mark_as_read(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainMessage.MarkAsReadRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	response, err := messageService.MarkAsRead(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Mark as read failed", nil, err)
	}

	return createResponse(200, "SUCCESS", response.Status, response, nil)
}

//export message_star
func message_star(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainMessage.StarRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	request.IsStarred = true
	err := messageService.StarMessage(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Star message failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Starred message successfully", nil, nil)
}

//export message_unstar
func message_unstar(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainMessage.StarRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	request.IsStarred = false
	err := messageService.StarMessage(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Unstar message failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Unstarred message successfully", nil, nil)
}

// ============ NEWSLETTER APIs ============

//export newsletter_unfollow
func newsletter_unfollow(requestJson *C.char) *C.char {
	ctx := context.Background()
	requestStr := cStringToGoString(requestJson)

	var request domainNewsletter.UnfollowRequest
	if err := json.Unmarshal([]byte(requestStr), &request); err != nil {
		return createResponse(400, "INVALID_JSON", "Invalid JSON format", nil, err)
	}

	err := newsletterService.Unfollow(ctx, request)
	if err != nil {
		return createResponse(500, "ERROR", "Unfollow newsletter failed", nil, err)
	}

	return createResponse(200, "SUCCESS", "Success unfollow newsletter", nil, nil)
}

// ============ MEMORY MANAGEMENT ============

//export free_string
func free_string(ptr *C.char) {
	C.free(unsafe.Pointer(ptr))
}

func main() {
	// This is required for CGO
	log.Println("WhatsApp FFI library loaded")
}
