# WhatsApp FFI Rust Workspace

这个项目提供了一个完整的Rust FFI接口，用于访问[go-whatsapp-web-multidevice](https://github.com/aldinokemal/go-whatsapp-web-multidevice)的所有API功能。通过这个FFI库，你可以在Rust代码中直接调用WhatsApp API，而无需通过HTTP请求。

## 项目结构

```
hivers/
├── Cargo.toml                 # Workspace配置
├── whatsapp-ffi/             # FFI核心库
│   ├── Cargo.toml
│   ├── build.rs              # Go代码编译脚本
│   ├── go/                   # Go FFI代码
│   │   ├── go.mod
│   │   └── ffi.go           # 所有53个API的C导出函数
│   └── src/                  # Rust FFI绑定
│       ├── lib.rs
│       ├── types.rs         # 所有数据类型定义
│       ├── app.rs           # App API (6个)
│       ├── send.rs          # Send API (11个)
│       ├── user.rs          # User API (9个)
│       ├── chat.rs          # Chat API (3个)
│       ├── group.rs         # Group API (17个)
│       ├── message.rs       # Message API (7个)
│       └── newsletter.rs    # Newsletter API (1个)
└── example-usage/            # 使用示例
    ├── Cargo.toml
    └── src/
        └── main.rs          # 完整的使用示例
```

## 支持的API

### 🔐 App APIs (6个)
- `app::App::login()` - 获取QR码登录
- `app::App::login_with_code(phone)` - 手机号码配对登录
- `app::App::logout()` - 登出
- `app::App::reconnect()` - 重连
- `app::App::devices()` - 获取设备列表
- `app::App::status()` - 获取连接状态

### 📤 Send APIs (11个)
- `send::Send::message(request)` - 发送文本消息
- `send::Send::image(request)` - 发送图片
- `send::Send::file(request)` - 发送文件
- `send::Send::video(request)` - 发送视频
- `send::Send::contact(request)` - 发送联系人
- `send::Send::link(request)` - 发送链接
- `send::Send::location(request)` - 发送位置
- `send::Send::audio(request)` - 发送音频
- `send::Send::poll(request)` - 发送投票
- `send::Send::presence(request)` - 设置在线状态
- `send::Send::chat_presence(request)` - 发送打字状态

### 👤 User APIs (9个)
- `user::User::info(request)` - 获取用户信息
- `user::User::avatar(request)` - 获取用户头像
- `user::User::change_avatar(request)` - 更改头像
- `user::User::change_pushname(request)` - 更改昵称
- `user::User::my_privacy()` - 获取隐私设置
- `user::User::my_groups()` - 获取我的群组
- `user::User::my_newsletters()` - 获取我的频道
- `user::User::my_contacts()` - 获取我的联系人
- `user::User::check(request)` - 检查用户是否在WhatsApp
- `user::User::business_profile(request)` - 获取商业档案

### 💬 Chat APIs (3个)
- `chat::Chat::list(request)` - 获取聊天列表
- `chat::Chat::messages(request)` - 获取聊天消息
- `chat::Chat::pin(request)` - 置顶/取消置顶聊天

### 👥 Group APIs (17个)
- `group::Group::create(request)` - 创建群组
- `group::Group::join_with_link(request)` - 通过链接加入群组
- `group::Group::info_from_link(request)` - 从链接获取群组信息
- `group::Group::info(request)` - 获取群组信息
- `group::Group::leave(request)` - 离开群组
- `group::Group::add_participants(request)` - 添加成员
- `group::Group::remove_participants(request)` - 移除成员
- `group::Group::promote_participants(request)` - 提升为管理员
- `group::Group::demote_participants(request)` - 取消管理员
- `group::Group::list_participant_requests(request)` - 获取加群请求
- `group::Group::approve_participant_requests(request)` - 批准加群请求
- `group::Group::reject_participant_requests(request)` - 拒绝加群请求
- `group::Group::set_photo(request)` - 设置群组头像
- `group::Group::set_name(request)` - 设置群组名称
- `group::Group::set_locked(request)` - 设置群组锁定状态
- `group::Group::set_announce(request)` - 设置群组公告模式
- `group::Group::set_topic(request)` - 设置群组主题

### 📨 Message APIs (7个)
- `message::Message::react(request)` - 消息表情回应
- `message::Message::revoke(request)` - 撤回消息
- `message::Message::delete(request)` - 删除消息
- `message::Message::update(request)` - 编辑消息
- `message::Message::mark_as_read(request)` - 标记为已读
- `message::Message::star(request)` - 收藏消息
- `message::Message::unstar(request)` - 取消收藏

### 📰 Newsletter APIs (1个)
- `newsletter::Newsletter::unfollow(request)` - 取消关注频道

## 快速开始

### 1. 环境要求

- Rust 1.70+
- Go 1.21+
- CGO支持

### 2. 构建项目

```bash
# 克隆项目
git clone <your-repo>
cd hivers

# 构建FFI库
cargo build --release

# 运行示例
cargo run --bin example-usage
```

### 3. 基本使用

```rust
use whatsapp_ffi::*;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化WhatsApp客户端
    init()?;
    
    // 检查连接状态
    let status = app::App::status()?;
    println!("Connected: {}, Logged in: {}", status.is_connected, status.is_logged_in);
    
    // 如果未登录，获取QR码
    if !status.is_logged_in {
        let login_response = app::App::login()?;
        println!("QR Code: {}", login_response.qr_link);
    }
    
    // 发送消息
    let message_request = MessageRequest {
        phone: "1234567890".to_string(),
        message: "Hello from Rust!".to_string(),
        reply_message_id: None,
        duration: None,
        is_forwarded: None,
    };
    
    let response = send::Send::message(&message_request)?;
    println!("Message sent: {}", response.status);
    
    Ok(())
}
```

### 4. 高级用法

#### 发送图片
```rust
let image_request = ImageRequest {
    phone: "1234567890".to_string(),
    image_url: Some("https://example.com/image.jpg".to_string()),
    caption: Some("Check out this image!".to_string()),
    reply_message_id: None,
    compress: Some(true),
    duration: None,
    is_forwarded: None,
};

let response = send::Send::image(&image_request)?;
```

#### 创建群组
```rust
let create_group_request = CreateGroupRequest {
    name: "My Rust Group".to_string(),
    participants: vec!["1234567890".to_string(), "0987654321".to_string()],
};

let response = group::Group::create(&create_group_request)?;
println!("Group ID: {}", response.group_id);
```

#### 获取聊天列表
```rust
let chat_list_request = ListChatsRequest {
    limit: Some(10),
    offset: Some(0),
    search: None,
    has_media: None,
};

let chats = chat::Chat::list(&chat_list_request)?;
for chat in chats {
    println!("Chat: {} ({})", chat.name, chat.jid);
}
```

## 错误处理

库提供了完整的错误处理：

```rust
use whatsapp_ffi::WhatsAppError;

match send::Send::message(&request) {
    Ok(response) => println!("Success: {}", response.status),
    Err(WhatsAppError::NotInitialized) => println!("Please initialize first"),
    Err(WhatsAppError::Api { code, message }) => println!("API Error {}: {}", code, message),
    Err(WhatsAppError::Json(e)) => println!("JSON Error: {}", e),
    Err(WhatsAppError::FFI(e)) => println!("FFI Error: {}", e),
    Err(e) => println!("Other Error: {}", e),
}
```

## 特性

- ✅ **完整API覆盖**: 支持所有53个API端点
- ✅ **类型安全**: 完整的Rust类型定义
- ✅ **零拷贝**: 高效的FFI调用
- ✅ **错误处理**: 完善的错误类型和处理
- ✅ **异步支持**: 兼容tokio异步运行时
- ✅ **内存安全**: 自动内存管理
- ✅ **文档完整**: 详细的API文档和示例

## 注意事项

1. **初始化**: 使用任何API前必须先调用`init()`
2. **登录状态**: 大部分API需要先登录WhatsApp
3. **手机号格式**: 使用国际格式，如`1234567890`（不要以0开头）
4. **并发安全**: 库是线程安全的，可以在多线程环境中使用

## 许可证

本项目基于MIT许可证开源。

## 贡献

欢迎提交Issue和Pull Request！
