# WhatsApp FFI API Reference

这个文档详细描述了所有53个可用的API端点及其用法。

## 目录

- [初始化](#初始化)
- [App APIs](#app-apis)
- [Send APIs](#send-apis)
- [User APIs](#user-apis)
- [Chat APIs](#chat-apis)
- [Group APIs](#group-apis)
- [Message APIs](#message-apis)
- [Newsletter APIs](#newsletter-apis)

## 初始化

在使用任何API之前，必须先初始化WhatsApp客户端：

```rust
use whatsapp_ffi::*;

// 初始化客户端
init()?;

// 检查是否已初始化
if is_initialized() {
    println!("WhatsApp client is ready!");
}
```

## App APIs

### 1. `app::App::login()` - QR码登录

获取QR码用于登录WhatsApp Web。

```rust
let response = app::App::login()?;
println!("QR Code URL: {}", response.qr_link);
println!("Expires in: {} seconds", response.qr_duration);
```

**返回类型**: `LoginResponse`
```rust
pub struct LoginResponse {
    pub qr_link: String,     // QR码图片URL
    pub qr_duration: i32,    // QR码有效期（秒）
}
```

### 2. `app::App::login_with_code(phone)` - 手机号配对登录

使用手机号获取配对码进行登录。

```rust
let response = app::App::login_with_code("1234567890")?;
println!("Pairing code: {}", response.pair_code);
```

**参数**:
- `phone: &str` - 手机号（国际格式，不含+号）

**返回类型**: `LoginWithCodeResponse`
```rust
pub struct LoginWithCodeResponse {
    pub pair_code: String,   // 8位配对码
}
```

### 3. `app::App::logout()` - 登出

从WhatsApp登出。

```rust
app::App::logout()?;
println!("Logged out successfully");
```

### 4. `app::App::reconnect()` - 重连

重新连接到WhatsApp服务器。

```rust
app::App::reconnect()?;
println!("Reconnected successfully");
```

### 5. `app::App::devices()` - 获取设备列表

获取已连接的设备列表。

```rust
let devices = app::App::devices()?;
for device in devices {
    println!("Device: {} ({})", device.device_name, device.device_id);
}
```

**返回类型**: `Vec<DeviceInfo>`
```rust
pub struct DeviceInfo {
    pub device_id: String,
    pub device_name: String,
    pub device_type: String,
}
```

### 6. `app::App::status()` - 获取连接状态

获取当前连接和登录状态。

```rust
let status = app::App::status()?;
println!("Connected: {}", status.is_connected);
println!("Logged in: {}", status.is_logged_in);
println!("Device ID: {}", status.device_id);
```

**返回类型**: `ConnectionStatus`
```rust
pub struct ConnectionStatus {
    pub is_connected: bool,
    pub is_logged_in: bool,
    pub device_id: String,
}
```

## Send APIs

### 1. `send::Send::message(request)` - 发送文本消息

发送文本消息到指定联系人或群组。

```rust
let request = MessageRequest {
    phone: "1234567890".to_string(),
    message: "Hello, World!".to_string(),
    reply_message_id: None,
    duration: None,
    is_forwarded: None,
};

let response = send::Send::message(&request)?;
println!("Message sent: {}", response.status);
```

**请求类型**: `MessageRequest`
```rust
pub struct MessageRequest {
    pub phone: String,                    // 接收者手机号或群组ID
    pub message: String,                  // 消息内容
    pub reply_message_id: Option<String>, // 回复的消息ID
    pub duration: Option<i32>,            // 消息持续时间（秒）
    pub is_forwarded: Option<bool>,       // 是否为转发消息
}
```

### 2. `send::Send::image(request)` - 发送图片

发送图片消息。

```rust
let request = ImageRequest {
    phone: "1234567890".to_string(),
    image_url: Some("https://example.com/image.jpg".to_string()),
    caption: Some("Check this out!".to_string()),
    reply_message_id: None,
    compress: Some(true),
    duration: None,
    is_forwarded: None,
};

let response = send::Send::image(&request)?;
```

**请求类型**: `ImageRequest`
```rust
pub struct ImageRequest {
    pub phone: String,
    pub image_url: Option<String>,        // 图片URL
    pub caption: Option<String>,          // 图片说明
    pub reply_message_id: Option<String>,
    pub compress: Option<bool>,           // 是否压缩
    pub duration: Option<i32>,
    pub is_forwarded: Option<bool>,
}
```

### 3. `send::Send::file(request)` - 发送文件

发送文件消息。

```rust
let request = FileRequest {
    phone: "1234567890".to_string(),
    file_url: Some("https://example.com/document.pdf".to_string()),
    caption: Some("Important document".to_string()),
    reply_message_id: None,
    duration: None,
    is_forwarded: None,
};

let response = send::Send::file(&request)?;
```

### 4. `send::Send::video(request)` - 发送视频

发送视频消息。

```rust
let request = VideoRequest {
    phone: "1234567890".to_string(),
    video_url: Some("https://example.com/video.mp4".to_string()),
    caption: Some("Awesome video!".to_string()),
    reply_message_id: None,
    duration: None,
    is_forwarded: None,
};

let response = send::Send::video(&request)?;
```

### 5. `send::Send::contact(request)` - 发送联系人

发送联系人卡片。

```rust
let request = ContactRequest {
    phone: "1234567890".to_string(),
    contact_name: "John Doe".to_string(),
    contact_phone: "0987654321".to_string(),
    reply_message_id: None,
    duration: None,
    is_forwarded: None,
};

let response = send::Send::contact(&request)?;
```

### 6. `send::Send::link(request)` - 发送链接

发送链接消息。

```rust
let request = LinkRequest {
    phone: "1234567890".to_string(),
    link: "https://example.com".to_string(),
    caption: Some("Check this website".to_string()),
    reply_message_id: None,
    duration: None,
    is_forwarded: None,
};

let response = send::Send::link(&request)?;
```

### 7. `send::Send::location(request)` - 发送位置

发送地理位置。

```rust
let request = LocationRequest {
    phone: "1234567890".to_string(),
    latitude: 37.7749,
    longitude: -122.4194,
    name: Some("San Francisco".to_string()),
    address: Some("San Francisco, CA, USA".to_string()),
    reply_message_id: None,
    duration: None,
    is_forwarded: None,
};

let response = send::Send::location(&request)?;
```

### 8. `send::Send::audio(request)` - 发送音频

发送音频消息。

```rust
let request = AudioRequest {
    phone: "1234567890".to_string(),
    audio_url: Some("https://example.com/audio.mp3".to_string()),
    reply_message_id: None,
    duration: None,
    is_forwarded: None,
};

let response = send::Send::audio(&request)?;
```

### 9. `send::Send::poll(request)` - 发送投票

发送投票消息。

```rust
let request = PollRequest {
    phone: "1234567890".to_string(),
    question: "What's your favorite color?".to_string(),
    options: vec!["Red".to_string(), "Blue".to_string(), "Green".to_string()],
    selectable_count: Some(1),
    reply_message_id: None,
    duration: None,
    is_forwarded: None,
};

let response = send::Send::poll(&request)?;
```

### 10. `send::Send::presence(request)` - 设置在线状态

设置全局在线状态。

```rust
let request = PresenceRequest {
    state: "available".to_string(), // "available", "unavailable", "composing", "recording", "paused"
};

let response = send::Send::presence(&request)?;
```

### 11. `send::Send::chat_presence(request)` - 发送打字状态

在特定聊天中发送打字状态。

```rust
let request = ChatPresenceRequest {
    phone: "1234567890".to_string(),
    state: "composing".to_string(), // "composing", "paused"
};

let response = send::Send::chat_presence(&request)?;
```

**所有Send API的返回类型**: `SendResponse`
```rust
pub struct SendResponse {
    pub status: String,
    pub message_id: Option<String>,
    pub timestamp: Option<i64>,
}
```

## User APIs

### 1. `user::User::info(request)` - 获取用户信息

获取指定用户的详细信息。

```rust
let request = UserInfoRequest {
    phone: "1234567890".to_string(),
};

let info = user::User::info(&request)?;
println!("Name: {}", info.name);
println!("Status: {}", info.status);
```

### 2. `user::User::avatar(request)` - 获取用户头像

获取用户头像URL。

```rust
let request = AvatarRequest {
    phone: "1234567890".to_string(),
};

let avatar = user::User::avatar(&request)?;
println!("Avatar URL: {}", avatar.url);
```

### 3. `user::User::change_avatar(request)` - 更改头像

更改自己的头像。

```rust
let request = ChangeAvatarRequest {
    avatar_url: Some("https://example.com/new-avatar.jpg".to_string()),
};

user::User::change_avatar(&request)?;
```

### 4. `user::User::change_pushname(request)` - 更改昵称

更改自己的显示名称。

```rust
let request = ChangePushNameRequest {
    push_name: "New Display Name".to_string(),
};

user::User::change_pushname(&request)?;
```

### 5. `user::User::my_privacy()` - 获取隐私设置

获取自己的隐私设置。

```rust
let privacy = user::User::my_privacy()?;
println!("Read receipts: {}", privacy.read_receipts);
println!("Profile photo: {}", privacy.profile_photo);
```

### 6. `user::User::my_groups()` - 获取我的群组

获取自己加入的所有群组。

```rust
let groups = user::User::my_groups()?;
for group in groups {
    println!("Group: {} ({})", group.name, group.jid);
}
```

### 7. `user::User::my_newsletters()` - 获取我的频道

获取自己关注的频道列表。

```rust
let newsletters = user::User::my_newsletters()?;
println!("Found {} newsletters", newsletters.len());
```

### 8. `user::User::my_contacts()` - 获取我的联系人

获取自己的联系人列表。

```rust
let contacts = user::User::my_contacts()?;
for contact in contacts {
    println!("Contact: {} ({})", contact.name, contact.phone);
}
```

### 9. `user::User::check(request)` - 检查用户是否在WhatsApp

检查一个或多个手机号是否注册了WhatsApp。

```rust
let request = CheckUserRequest {
    phones: vec!["1234567890".to_string(), "0987654321".to_string()],
};

let results = user::User::check(&request)?;
for result in results {
    println!("{}: {}", result.phone, if result.exists { "On WhatsApp" } else { "Not on WhatsApp" });
}
```

### 10. `user::User::business_profile(request)` - 获取商业档案

获取商业账户的详细信息。

```rust
let request = BusinessProfileRequest {
    phone: "1234567890".to_string(),
};

let profile = user::User::business_profile(&request)?;
println!("Business: {}", profile.name);
println!("Category: {}", profile.category);
```

## Chat APIs

### 1. `chat::Chat::list(request)` - 获取聊天列表

获取聊天列表，支持分页和搜索。

```rust
let request = ListChatsRequest {
    limit: Some(20),
    offset: Some(0),
    search: Some("John".to_string()),
    has_media: Some(false),
};

let chats = chat::Chat::list(&request)?;
for chat in chats {
    println!("Chat: {} - Unread: {}", chat.name, chat.unread_count);
}
```

### 2. `chat::Chat::messages(request)` - 获取聊天消息

获取指定聊天的消息历史。

```rust
let request = GetChatMessagesRequest {
    chat_jid: "<EMAIL>".to_string(),
    limit: Some(50),
    offset: Some(0),
    media_only: Some(false),
    search: Some("hello".to_string()),
    start_time: None,
    end_time: None,
    is_from_me: None,
};

let messages = chat::Chat::messages(&request)?;
for message in messages {
    println!("{}: {}", message.from, message.message);
}
```

### 3. `chat::Chat::pin(request)` - 置顶聊天

置顶或取消置顶聊天。

```rust
let request = PinChatRequest {
    chat_jid: "<EMAIL>".to_string(),
    pin: true,
};

let response = chat::Chat::pin(&request)?;
println!("Pin result: {}", response.message);
```

## Group APIs

群组API提供了完整的群组管理功能，包括创建、加入、管理成员等。

### 1. `group::Group::create(request)` - 创建群组

```rust
let request = CreateGroupRequest {
    name: "My New Group".to_string(),
    participants: vec!["1234567890".to_string(), "0987654321".to_string()],
};

let response = group::Group::create(&request)?;
println!("Group created: {}", response.group_id);
```

### 2. `group::Group::join_with_link(request)` - 通过链接加入群组

```rust
let request = JoinGroupWithLinkRequest {
    link: "https://chat.whatsapp.com/XXXXXXXXXX".to_string(),
};

let response = group::Group::join_with_link(&request)?;
println!("Joined group: {}", response.group_id);
```

### 3-17. 其他群组API

包括获取群组信息、离开群组、管理成员、设置群组属性等功能。详细用法请参考代码示例。

## Message APIs

消息API提供了对已发送消息的各种操作。

### 1. `message::Message::react(request)` - 消息表情回应

```rust
let request = ReactionRequest {
    message_id: "message123".to_string(),
    phone: "1234567890".to_string(),
    emoji: "👍".to_string(),
};

let response = message::Message::react(&request)?;
```

### 2-7. 其他消息API

包括撤回、删除、编辑、标记已读、收藏等功能。

## Newsletter APIs

### 1. `newsletter::Newsletter::unfollow(request)` - 取消关注频道

```rust
let request = UnfollowRequest {
    newsletter_id: "newsletter123".to_string(),
};

newsletter::Newsletter::unfollow(&request)?;
```

## 错误处理

所有API都返回`Result<T, WhatsAppError>`，支持完整的错误处理：

```rust
match send::Send::message(&request) {
    Ok(response) => println!("Success: {}", response.status),
    Err(WhatsAppError::NotInitialized) => println!("Please call init() first"),
    Err(WhatsAppError::Api { code, message }) => println!("API Error {}: {}", code, message),
    Err(e) => println!("Error: {}", e),
}
```

## 注意事项

1. **手机号格式**: 使用国际格式，不包含+号，如`1234567890`
2. **群组ID格式**: 群组ID通常以`@g.us`结尾
3. **消息ID**: 消息ID是WhatsApp生成的唯一标识符
4. **URL支持**: 图片、视频、音频、文件都支持URL方式发送
5. **异步操作**: 所有API都是同步的，但可以在异步环境中使用
